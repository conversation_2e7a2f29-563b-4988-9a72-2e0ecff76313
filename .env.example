# Daily Catalyst Environment Variables
# Copy this file to .env and fill in your API keys

# Deepgram API Key (required for real-time transcription)
# Get free $200 credits at: https://console.deepgram.com/
DEEPGRAM_API_KEY=****************************************

# Optional: Other STT Services
# GOOGLE_CLOUD_API_KEY=your_google_api_key_here
# AZURE_SPEECH_KEY=your_azure_speech_key_here
# AZURE_SPEECH_REGION=your_azure_region_here

# Optional: LLM Services for advanced analysis
# OPENAI_API_KEY=your_openai_api_key_here
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Google Gemini API for off-topic detection (required for meeting analysis)
# Get free API key at: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Application Settings
DEBUG=false
LOG_LEVEL=INFO

# Audio Processing Settings
DEFAULT_SAMPLE_RATE=16000
DEFAULT_CHUNK_DURATION_MS=100
DEFAULT_SPEED_MULTIPLIER=1.0

# Meeting Analysis Settings
OFF_TOPIC_THRESHOLD=0.6
DEEP_DIVE_DURATION_SECONDS=30
MAX_SPEAKERS=10
