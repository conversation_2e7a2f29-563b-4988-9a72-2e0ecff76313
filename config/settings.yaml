# Technical Settings Configuration for Daily Catalyst
# This file contains all technical parameters for live transcription and analysis

# API Configuration
api:
  # Deepgram API settings
  deepgram:
    model: "nova-3"
    language: "en-US"
    smart_format: true
    diarize: true
    interim_results: false
    punctuate: true
    encoding: "linear16"
    sample_rate: 16000
    channels: 1
    filler_words: false
    numerals: true

    # Enhanced diarization settings (based on Deepgram best practices)
    # Reference: https://deepgram.com/learn/creating-speaker-labeled-transcripts-with-google-colab
    diarization:
      enabled: true
      min_speakers: 2  # Minimum expected speakers in meeting
      max_speakers: 8  # Maximum expected speakers in meeting
      # Note: For best diarization results:
      # - Use high-quality audio (16kHz+ sample rate)
      # - Ensure speakers have distinct voices
      # - Minimize background noise and cross-talk

    # Additional transcription features (for batch/pre-recorded only)
    # Note: Live transcription has limited feature support
    features:
      paragraphs: true  # Group sentences into paragraphs (batch only)
      utterances: true  # Detect natural speech boundaries (batch only)
      sentiment: false  # Sentiment analysis (batch only)
      topics: false     # Topic detection (batch only)

    # Connection settings
    keepalive: true
    verbose_logging: false  # Set to CRITICAL level

    # WebSocket connection (alternative to SDK)
    websocket:
      enable_direct_websocket: true   # Use direct WebSocket instead of SDK
      url_template: "wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true&model={model}&encoding=linear16&sample_rate=16000"
      auth_header: "Authorization: Token {api_key}"

  # Gemini API settings
  gemini:
    # Model fallback system - ordered by preference
    models:
      - name: "gemini-2.5-flash"
        display_name: "Gemini 2.5 Flash"
        rpm_limit: 8  # Conservative limit (10 RPM actual)
        daily_limit: 200  # Conservative limit (250 RPD actual)

      - name: "gemini-2.0-flash"
        display_name: "Gemini 2.0 Flash"
        rpm_limit: 12  # Conservative limit (15 RPM actual)
        daily_limit: 150  # Conservative limit (200 RPD actual)

      - name: "gemma-3n-e2b-it"
        display_name: "Gemma 3n-e2b"
        rpm_limit: 25  # Conservative limit (30 RPM actual)
        daily_limit: 1000  # Conservative limit (14,400 RPD actual)

    # Generation settings
    temperature: 0.7
    max_tokens: 500
    top_p: 0.9
    top_k: 40

# Audio Processing Configuration
audio:
  # FFmpeg settings for live audio capture
  # Note: daily-catalyst uses FFmpeg for server-side audio processing
  # For browser-based apps, consider WebSocket + MediaRecorder API
  # Reference: https://deepgram.com/learn/build-a-real-time-transcription-app-with-react-and-deepgram
  ffmpeg:
    input_format: "pulse"
    output_format: "s16le"
    codec: "pcm_s16le"
    sample_rate: 16000
    channels: 1
    flush_packets: true

  # Audio source settings
  source:
    default_name: "RDPSink.monitor"
    env_var: "AUDIO_SOURCE"

    # Live microphone settings (PyAudio)
    microphone:
      enable_live_mic: false  # Enable live microphone capture
      device_index: null      # Auto-detect default microphone
      chunk_size: 8000        # PyAudio chunk size (larger for real-time)
      format: "paInt16"       # PyAudio format

  # Streaming settings
  streaming:
    chunk_size: 2048  # bytes
    chunk_duration_ms: 250  # milliseconds - optimized for real-time responsiveness (based on Deepgram best practices)
    speed_multiplier: 1.0   # Audio playback speed multiplier
                            # 1.0 = real-time (realistic meeting simulation)
                            # 0.8 = slower for better accuracy, 1.2 = faster preview
    buffer_size: 5.0  # seconds

    # Alternative chunk sizes for different use cases
    chunk_options:
      ultra_responsive: 250  # Best for real-time UI feedback
      balanced: 500          # Good balance of responsiveness and accuracy
      high_accuracy: 1000    # Better for speaker diarization

    # Speed multiplier profiles for different scenarios
    speed_profiles:
      real_time: 1.0         # Normal speed - realistic meeting simulation (current default)
      high_quality: 0.8      # 20% slower - better accuracy for complex audio
      ultra_quality: 0.6     # 40% slower - maximum accuracy for difficult audio
      fast_preview: 1.2      # 20% faster - quick previews (may reduce accuracy)

# Analysis Configuration
analysis:
  # Text analysis thresholds
  text_analysis:
    min_segment_length: 30  # characters
    short_segment_threshold: 80  # characters
    very_long_threshold: 300  # characters
    suspicious_length_threshold: 120  # characters

  # AI analysis triggers
  ai_analysis:
    # When to use AI vs local analysis
    use_ai_for_suspicious: true
    use_ai_for_long_segments: true
    min_suspicious_indicators: 1

    # Cache settings
    enable_caching: true
    cache_size_limit: 1000

    # Background analysis
    enable_background_analysis: true
    background_analysis_timeout: 30  # seconds

  # Local fallback analysis
  local_analysis:
    # Thresholds for local analysis (when AI unavailable)
    technical_threshold: 1  # minimum obvious technical indicators
    meta_threshold: 1  # minimum obvious meta indicators
    length_threshold: 80  # minimum length for technical flagging
    very_long_threshold: 300  # automatic flag for very long segments

    # Enable/disable local analysis features
    enable_keyword_detection: true
    enable_length_analysis: true
    enable_context_analysis: false

# Performance Configuration
performance:
  # Connection timeouts
  timeouts:
    deepgram_connection: 30  # seconds
    gemini_request: 30  # seconds
    ffmpeg_startup: 10  # seconds

  # Retry settings
  retries:
    max_retries: 3
    retry_delay: 1.0  # seconds
    exponential_backoff: true

  # Memory management
  memory:
    max_transcript_history: 1000  # segments
    cleanup_interval: 300  # seconds

  # Threading and Async
  threading:
    enable_background_threads: true
    max_background_threads: 3
    thread_timeout: 60  # seconds

  # Async processing (inspired by AI Scribe example)
  async_processing:
    enable_async_mode: true   # Enable async audio processing
    queue_size: 1000          # Audio queue size
    task_timeout: 30          # seconds
    concurrent_tasks: 3       # sender, receiver, microphone

# Display Configuration
display:
  # Output formatting
  formatting:
    timestamp_format: "[{:.1f}s]"
    speaker_format: "{}"

  # Status indicators
  indicators:
    on_topic: "✅"
    off_topic: "🚨"
    ai_analyzing: "🔄"
    ai_result: "🤖"
    ai_update: "🔄"
    ai_correction: "🔄"
    quota_exhausted: "💰"

  # Progress reporting
  progress:
    show_chunk_progress: false  # too verbose
    show_connection_status: true
    show_model_switches: true
    show_quota_status: true

# Logging Configuration
logging:
  # Log levels for different components
  levels:
    root: "INFO"
    deepgram: "CRITICAL"  # Disable Deepgram SDK logs
    realtime_audio_streamer: "CRITICAL"
    ffmpeg: "WARNING"

  # Log formatting
  format: "%(asctime)s - %(levelname)s - %(message)s"

  # File logging
  file_logging:
    enabled: true
    filename: "logs/live_transcription.log"
    max_size: "10MB"
    backup_count: 5

# Development Configuration
development:
  # Debug settings
  debug:
    enable_debug_mode: false
    save_intermediate_files: false
    profile_performance: false
    verbose_ai_responses: false

  # Testing settings
  testing:
    enable_test_mode: false
    mock_ai_responses: false
    simulate_quota_limits: false

  # Experimental features
  experimental:
    enable_experimental_features: false
    new_analysis_algorithms: false
    enhanced_speaker_detection: false