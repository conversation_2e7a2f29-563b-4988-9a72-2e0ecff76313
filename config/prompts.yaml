# AI Prompts Configuration for Daily Catalyst
# This file contains all AI prompts used for off-topic detection and analysis

# Off-topic detection prompts
off_topic_detection:
  # Main real-time analysis prompt for Gemini
  realtime_prompt: |
    You are a strict Scrum Master analyzing a daily standup. Flag technical deep-dives as OFF-TOPIC.

    MEETING CONTEXT:
    {context}

    NEW STATEMENT:
    {new_phrase}

    CRITICAL: These are OFF-TOPIC in standups:

    1. TECHNICAL IMPLEMENTATION DETAILS:
       - CSS specifics: "flexbox inside flexbox", "width of 0", "missing prop"
       - Code details: debugging, stack traces, specific syntax
       - Architecture: implementation explanations

    2. PROBLEM-SOLVING ATTEMPTS:
       - "How should I fix this?"
       - Asking for technical help during standup

    3. PROCESS META-DISCUSSIONS:
       - "improve meeting efficiency"
       - Workflow changes

    EXAMPLES OF OFF-TOPIC:
    - "The flexbox was inside another flexbox"
    - "Which had a width of 0 because of a missing prop"
    - "The CSS property wasn't working"

    ON-TOPIC standup content:
    - "I worked on the frontend"
    - "I'm blocked on CSS issues"
    - "I'll fix the styling today"

    Answer ONLY:
    - "OFF_TOPIC: Technical implementation detail" for CSS/code specifics
    - "OFF_TOPIC: [reason]" for other violations
    - "ON_TOPIC" for proper standup updates

  # Alternative prompts for A/B testing
  alternative_prompts:
    # Stricter version with violation codes
    strict_architect: |
      You are the "Architect of Efficiency" analyzing daily standup meetings. Your role is to identify violations of standup principles with specific violation codes.

      MEETING CONTEXT:
      {context}

      NEW STATEMENT:
      {new_phrase}

      VIOLATION CODES:
      - TECHNICAL_DEEP_DIVE: Excessive technical implementation details
      - LIVE_PROBLEM_SOLVING: Attempting to solve problems during standup
      - VAGUE_UPDATE: Non-specific or unclear progress updates
      - EXCUSES_AND_NARRATIVES: Long explanations for delays or issues
      - META_DISCUSSION: Discussions about meeting process or workflow
      - IRRELEVANT_INFO: Information not related to current sprint work

      ANALYSIS CRITERIA:
      Focus on CONTEXT over keywords. Consider:
      - Does this advance understanding of sprint progress?
      - Is this actionable information for the team?
      - Would this be better discussed offline?

      RESPONSE FORMAT:
      - "OFF_TOPIC: [VIOLATION_CODE] - [brief explanation]"
      - "ON_TOPIC" for appropriate standup content

    # Lenient version for comparison
    lenient_facilitator: |
      You are a supportive meeting facilitator analyzing standup content. Only flag clearly excessive violations.

      MEETING CONTEXT:
      {context}

      NEW STATEMENT:
      {new_phrase}

      Flag as OFF-TOPIC only if:
      1. Extremely detailed technical explanations (>100 words)
      2. Clear attempts to solve complex problems in real-time
      3. Obvious meta-discussions about meeting process

      Be lenient with:
      - Brief technical mentions
      - Short clarifying questions
      - Natural conversation flow

      RESPONSE:
      - "OFF_TOPIC: [reason]" only for clear violations
      - "ON_TOPIC" for everything else

# Prompt versioning for A/B testing
prompt_versions:
  current_version: "realtime_prompt"
  available_versions:
    - name: "realtime_prompt"
      description: "Standard off-topic detection prompt"
      effectiveness_score: 0.85

    - name: "strict_architect"
      description: "Stricter analysis with violation codes"
      effectiveness_score: 0.90

    - name: "lenient_facilitator"
      description: "More permissive analysis"
      effectiveness_score: 0.75

# Prompt templates for different meeting types
meeting_type_prompts:
  daily_standup:
    prompt_key: "realtime_prompt"
    context_template: "Daily standup meeting - focus on sprint progress"

  retrospective:
    prompt_key: "lenient_facilitator"
    context_template: "Retrospective meeting - allow broader discussions"

  planning:
    prompt_key: "lenient_facilitator"
    context_template: "Planning meeting - technical details may be relevant"

# Fallback prompts for different scenarios
fallback_prompts:
  # When AI models are unavailable
  local_analysis_keywords:
    extremely_obvious_technical:
      - "width of 0 because of a missing prop"
      - "flexbox was inside another flexbox which had"
      - "stack trace shows"
      - "error message says"
      - "sql query returned"
      - "database connection failed"

    extremely_obvious_meta:
      - "we need to improve our meeting efficiency"
      - "this meeting is taking too long"
      - "we should change our workflow"

    suspicious_indicators:
      # Technical deep-dives
      - "flexbox"
      - "css"
      - "width of 0"
      - "missing prop"
      - "stack trace"
      - "implementation"
      - "algorithm"
      - "debugging"
      - "error message"

      # Problem-solving
      - "how should"
      - "what do you think"
      - "maybe we should"
      - "perhaps"
      - "should we use"
      - "what about"
      - "how about"

      # Meta-discussions
      - "meeting efficiency"
      - "process"
      - "workflow"
      - "improve our"
      - "we need to"
      - "we should change"

      # Long explanations
      - "because"
      - "the reason was"
      - "what happened"
      - "i spent"
      - "i had to"
      - "it took so long"

    normal_work_phrases:
      - "yesterday"
      - "today"
      - "working on"
      - "completed"
      - "finished"
      - "will do"
      - "planning to"
      - "blocked by"
      - "waiting for"
      - "testing"
      - "reviewing"
      - "meeting"
      - "sync"
      - "call"

# Prompt customization settings
customization:
  # Allow users to override prompts
  allow_custom_prompts: true
  custom_prompts_dir: "config/custom_prompts"

  # Prompt validation
  validate_prompts: true
  required_placeholders:
    - "{context}"
    - "{new_phrase}"

  # Prompt performance tracking
  track_effectiveness: true
  effectiveness_metrics:
    - "accuracy"
    - "false_positive_rate"
    - "false_negative_rate"
    - "response_time"