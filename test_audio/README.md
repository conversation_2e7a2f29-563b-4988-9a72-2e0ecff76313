# Test Audio Files

This directory contains audio files for testing Daily Catalyst functionality.

## 📁 Files

### `meeting_example.wav`
- **Source**: Copied from original Daily Catalyst project
- **Format**: RIFF WAVE, Microsoft PCM, 16-bit, stereo, 48kHz
- **Duration**: ~9.3 minutes
- **Size**: ~28.5 MB
- **Content**: Example meeting recording for testing transcription and speaker diarization

## 🎯 Usage

### Default Example
Most scripts use `meeting_example.wav` as the default audio file:

```bash
# Uses meeting_example.wav by default
python examples/deepgram_realtime_demo.py

# Explicit usage
python examples/deepgram_realtime_demo.py test_audio/meeting_example.wav
```

### Custom Audio Files
You can add your own audio files to this directory:

```bash
# Copy your audio file
cp your_meeting.wav test_audio/

# Use with any script
python examples/deepgram_realtime_demo.py test_audio/your_meeting.wav
```

## 🎵 Supported Formats

Daily Catalyst supports various audio formats:
- **WAV** (recommended for best quality)
- **MP3** (compressed, good for smaller files)
- **M4A** (Apple format)
- **FLAC** (lossless compression)
- **OGG** (open source format)

## 🔧 Audio Requirements

For best results with speech recognition:
- **Sample Rate**: 16kHz or higher (48kHz is fine)
- **Channels**: Mono preferred, stereo supported
- **Bit Depth**: 16-bit minimum
- **Quality**: Clear speech, minimal background noise

## 📊 Audio Information

To check audio file properties:

```bash
# Using file command
file test_audio/meeting_example.wav

# Using Python (in virtual environment)
python -c "
import soundfile as sf
info = sf.info('test_audio/meeting_example.wav')
print(f'Duration: {info.duration:.1f}s')
print(f'Sample Rate: {info.samplerate}Hz')
print(f'Channels: {info.channels}')
"
```

## ⚠️ Note

Audio files are excluded from git repository (see `.gitignore`). 
The `meeting_example.wav` file should be copied manually or downloaded separately.

## 🎬 Creating Test Audio

To create your own test audio:

1. **Record a meeting** (with permission from participants)
2. **Convert to supported format**:
   ```bash
   # Convert to 16kHz mono WAV
   ffmpeg -i input.mp3 -ar 16000 -ac 1 output.wav
   ```
3. **Place in test_audio directory**
4. **Test with Daily Catalyst**

## 🔐 Privacy

- Never commit real meeting recordings to git
- Use anonymized or synthetic audio for testing
- Respect privacy and confidentiality of meeting participants
- Consider using generated speech for public examples
