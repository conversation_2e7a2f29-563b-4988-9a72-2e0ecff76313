# Deepgram Real-Time Integration для Daily Catalyst

## 🎯 Обзор

Интеграция Deepgram обеспечивает real-time транскрипцию и speaker diarization для Daily Catalyst с использованием WebSocket API Deepgram.

### ✨ Возможности

- **Real-time транскрипция** с латентностью < 300ms
- **Speaker diarization** в реальном времени
- **Smart formatting** и автоматическая пунктуация
- **Interim results** для мгновенной обратной связи
- **Meeting analysis** с обнаружением off-topic дискуссий
- **Live statistics** и speaker activity tracking

## 🏗️ Архитектура

```
Audio File → RealTimeAudioStreamer → WebSocket → Deepgram API
                                                      ↓
Meeting Insights ← Meeting Analyzer ← Speaker System ← Transcription
```

### Компоненты

1. **DeepgramSTTService** - WebSocket подключение к Deepgram API
2. **RealTimeAudioStreamer** - Эмуляция real-time аудио из файла
3. **RealTimeMeetingProcessor** - Интеграция всех компонентов
4. **Meeting Analysis** - Анализ встреч и генерация insights

## 🚀 Быстрый старт

### 1. Установка зависимостей

```bash
pip install websockets>=10.0 pydub>=0.25.0 aiohttp>=3.8.0
```

### 2. Получение API ключа

1. Зарегистрируйтесь на [Deepgram Console](https://console.deepgram.com/)
2. Получите бесплатный API ключ ($200 кредитов)
3. Установите переменную окружения:

```bash
export DEEPGRAM_API_KEY="your_api_key_here"
```

### 3. Запуск демо

```bash
# Базовое демо с примером аудио
python examples/deepgram_realtime_demo.py

# Или с вашим аудио файлом
python examples/deepgram_realtime_demo.py path/to/your/audio.wav
```

## 📊 Пример использования

### Простая интеграция

```python
import asyncio
from src.realtime_meeting_processor import RealTimeMeetingProcessor

async def main():
    # Инициализация
    processor = RealTimeMeetingProcessor(
        deepgram_api_key="your_api_key",
        audio_file_path="test_audio/meeting_example.wav",
        chunk_duration_ms=100,
        speed_multiplier=1.0
    )
    
    # Callbacks
    def on_transcript(segment):
        print(f"{segment.speaker_id}: {segment.text}")
    
    def on_insight(insight):
        print(f"💡 {insight['type']}: {insight['message']}")
    
    processor.on_transcript_update = on_transcript
    processor.on_meeting_insight = on_insight
    
    # Запуск
    if await processor.start_processing():
        await asyncio.sleep(60)  # Обработка 1 минуты
        await processor.stop_processing()

asyncio.run(main())
```

### Продвинутое использование

```python
# Настройка Deepgram сервиса
deepgram_service = DeepgramSTTService(
    api_key="your_api_key",
    model="nova-3",              # Лучшая модель
    language="en-US",            # Язык
    enable_diarization=True,     # Speaker diarization
    enable_smart_format=True,    # Умное форматирование
    enable_interim_results=True  # Промежуточные результаты
)

# Настройка аудио стримера
audio_streamer = RealTimeAudioStreamer(
    chunk_duration_ms=100,       # 100ms чанки
    target_sample_rate=16000,    # 16kHz
    target_channels=1,           # Mono
    speed_multiplier=1.5         # 1.5x скорость
)
```

## 🎛️ Конфигурация

### Deepgram модели

| Модель | Описание | Цена (streaming) |
|--------|----------|------------------|
| `nova-3` | Лучшая точность и скорость | $0.0077/мин |
| `nova-2` | Хорошая точность | $0.0058/мин |
| `enhanced` | Для сложных слов | $0.0165/мин |
| `base` | Базовая модель | $0.0145/мин |

### Языки

Поддерживается 36+ языков:
- `en-US` - Английский (США)
- `en-GB` - Английский (Великобритания)
- `ru` - Русский
- `es` - Испанский
- `fr` - Французский
- И многие другие...

### Дополнительные функции

```python
# Speaker diarization
enable_diarization=True          # +$0.0020/мин

# Smart formatting
enable_smart_format=True         # Включено в базовую цену

# Filler words detection
filler_words=True               # "uh", "um" и т.д.

# Profanity filtering
profanity_filter=True           # Фильтр нецензурной лексики
```

## 💰 Стоимость

### Для daily standup (1 час)

- **Базовая транскрипция**: ~$0.46
- **С speaker diarization**: ~$0.58
- **Очень доступно!** 💚

### Расчет для команды

```python
# 5 человек, 15 минут daily standup, 22 рабочих дня
monthly_cost = 0.25 * 0.0097 * 22  # ~$0.05/месяц
yearly_cost = monthly_cost * 12     # ~$0.60/год
```

## 📈 Мониторинг и статистика

### Live статистика

```python
stats = processor.get_live_stats()

print(f"Speakers: {len(stats['speaker_percentages'])}")
print(f"Segments: {stats['meeting_stats'].total_segments}")
print(f"Off-topic: {stats['meeting_stats'].off_topic_segments}")
print(f"Deep-dives: {stats['meeting_stats'].technical_deep_dives}")
```

### Speaker breakdown

```python
for speaker_id, stats in speaker_percentages.items():
    print(f"{speaker_id}: {stats['percentage']:.1f}% "
          f"({stats['total_time']:.1f}s)")
```

## 🔧 Troubleshooting

### Частые проблемы

**1. WebSocket connection failed**
```bash
# Проверьте API ключ
echo $DEEPGRAM_API_KEY

# Проверьте интернет соединение
ping api.deepgram.com
```

**2. Audio format issues**
```python
# Убедитесь, что аудио в правильном формате
# 16kHz, mono, 16-bit PCM
```

**3. Import errors**
```bash
# Установите зависимости
pip install -r requirements.txt
```

### Логирование

```python
import logging

# Включить debug логи
logging.basicConfig(level=logging.DEBUG)

# Отключить verbose логи WebSocket
logging.getLogger('websockets').setLevel(logging.WARNING)
```

## 🎯 Интеграция с Daily Catalyst

### Замена существующего STT

```python
# Вместо существующего STT сервиса
from src.deepgram_stt_service import DeepgramSTTService

# В вашем коде
stt_service = DeepgramSTTService(
    api_key=os.getenv('DEEPGRAM_API_KEY'),
    enable_diarization=True
)
```

### Интеграция с Speaker System

```python
# Связь с существующей speaker system
def map_deepgram_speaker_to_known(deepgram_speaker_id):
    # Ваша логика сопоставления
    return known_speaker_mapping.get(deepgram_speaker_id, "unknown")
```

## 📚 API Reference

### DeepgramSTTService

```python
class DeepgramSTTService:
    def __init__(self, api_key, model="nova-3", language="en-US", ...)
    async def connect() -> bool
    async def disconnect()
    async def send_audio(audio_data: bytes)
    def get_stats() -> Dict[str, Any]
```

### RealTimeAudioStreamer

```python
class RealTimeAudioStreamer:
    def __init__(self, chunk_duration_ms=100, speed_multiplier=1.0, ...)
    def load_audio_file(file_path: str) -> bool
    async def start_streaming() -> bool
    async def stop_streaming()
    def get_progress() -> dict
```

### RealTimeMeetingProcessor

```python
class RealTimeMeetingProcessor:
    def __init__(self, deepgram_api_key, audio_file_path, ...)
    async def start_processing() -> bool
    async def stop_processing()
    def get_live_stats() -> Dict[str, Any]
```

## 🔗 Полезные ссылки

- [Deepgram Documentation](https://developers.deepgram.com/)
- [Deepgram Console](https://console.deepgram.com/)
- [Deepgram Pricing](https://deepgram.com/pricing)
- [WebSocket API Reference](https://developers.deepgram.com/reference/speech-to-text-api/listen-streaming)
- [Models Overview](https://developers.deepgram.com/docs/models-overview)

## 🤝 Поддержка

Если у вас есть вопросы или проблемы:

1. Проверьте [Troubleshooting](#-troubleshooting)
2. Посмотрите [Deepgram Documentation](https://developers.deepgram.com/)
3. Создайте issue в репозитории
4. Обратитесь в [Deepgram Community](https://community.deepgram.com/)

---

**Deepgram предоставляет лучшее решение для real-time транскрипции в Daily Catalyst!** 🎯
