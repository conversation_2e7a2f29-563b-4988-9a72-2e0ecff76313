# Configuration System Documentation

Daily Catalyst features a flexible configuration system that allows you to customize AI prompts, technical parameters, and application behavior without modifying code.

## Overview

The configuration system consists of three main files:

- **`config/prompts.yaml`** - AI prompts for off-topic detection and analysis
- **`config/settings.yaml`** - Technical parameters (API settings, audio processing, analysis thresholds)
- **`config.yaml`** - Main application settings (existing file)

## Configuration Files

### 1. Prompts Configuration (`config/prompts.yaml`)

This file contains all AI prompts used for off-topic detection:

```yaml
off_topic_detection:
  realtime_prompt: |
    You are a strict Scrum Master analyzing a daily standup...

  alternative_prompts:
    strict_architect: |
      You are the "Architect of Efficiency"...
    lenient_facilitator: |
      You are a supportive meeting facilitator...

prompt_versions:
  current_version: "realtime_prompt"
  available_versions:
    - name: "realtime_prompt"
      effectiveness_score: 0.85
```

#### Key Sections:

- **`realtime_prompt`** - Main prompt used for analysis
- **`alternative_prompts`** - Different prompt variations for A/B testing
- **`prompt_versions`** - Version management and effectiveness tracking
- **`fallback_prompts`** - Keywords and phrases for local analysis when AI is unavailable

### 2. Settings Configuration (`config/settings.yaml`)

Technical parameters and API settings:

```yaml
api:
  deepgram:
    model: "nova-3"
    language: "en-US"
    smart_format: true
    diarize: true

  gemini:
    models:
      - name: "gemini-2.5-flash"
        rpm_limit: 8
        daily_limit: 200

audio:
  ffmpeg:
    input_format: "pulse"
    sample_rate: 16000
    channels: 1

  streaming:
    chunk_size: 2048
    chunk_duration_ms: 500

analysis:
  text_analysis:
    min_segment_length: 30
    suspicious_length_threshold: 120

  ai_analysis:
    use_ai_for_suspicious: true
    enable_caching: true
```

#### Key Sections:

- **`api`** - API configuration for Deepgram and Gemini
- **`audio`** - Audio processing and streaming settings
- **`analysis`** - Text analysis thresholds and AI analysis triggers
- **`performance`** - Timeouts, retries, and memory management
- **`display`** - Output formatting and status indicators

## Usage

### Loading Configuration

The configuration system is automatically loaded when you import the classes:

```python
from deepgram_live_demo import RealTimeOffTopicAnalyzer
from live_transcription import LiveTranscriber

# Configuration is loaded automatically
analyzer = RealTimeOffTopicAnalyzer(api_key)
transcriber = LiveTranscriber(deepgram_key, gemini_key)
```

### Manual Configuration Loading

You can also load configuration manually:

```python
from config.config_loader import load_config, get_prompt, get_setting

# Load all configurations
config = load_config()

# Get specific prompt
prompt = get_prompt("realtime_prompt")

# Get specific setting
chunk_size = get_setting("audio.streaming.chunk_size", 2048)
```

### Custom Configuration Loader

For advanced usage:

```python
from config.config_loader import ConfigLoader

# Create custom loader
loader = ConfigLoader("/path/to/config/directory")
config = loader.load_all_configs()

# Check for errors
if not config.is_valid:
    for error in config.errors:
        print(f"Error: {error}")
```

## Customization

### 1. Modifying Prompts

Edit `config/prompts.yaml` to change AI behavior:

```yaml
off_topic_detection:
  realtime_prompt: |
    Your custom prompt here.
    Use {context} and {new_phrase} placeholders.
```

### 2. A/B Testing Prompts

Switch between different prompts:

```yaml
prompt_versions:
  current_version: "strict_architect"  # Change this line
```

### 3. Adding Custom Prompts

Create files in `config/custom_prompts/`:

```bash
mkdir -p config/custom_prompts
cat > config/custom_prompts/my_prompts.yaml << EOF
my_custom_prompt: |
  Custom prompt for specific use case...
EOF
```

### 4. Adjusting Technical Settings

Modify `config/settings.yaml`:

```yaml
# Increase AI analysis sensitivity
analysis:
  text_analysis:
    min_segment_length: 20  # Lower threshold
    suspicious_length_threshold: 100

# Adjust audio quality
audio:
  ffmpeg:
    sample_rate: 22050  # Higher quality
  streaming:
    chunk_size: 4096  # Larger chunks
```

## Validation

The configuration system includes validation:

```python
from config.config_loader import load_config

config = load_config()

if not config.is_valid:
    print("Configuration errors:")
    for error in config.errors:
        print(f"  - {error}")

if config.warnings:
    print("Configuration warnings:")
    for warning in config.warnings:
        print(f"  - {warning}")
```

## Fallback Behavior

If configuration files are missing or invalid:

1. **Prompts** - Falls back to hardcoded default prompt
2. **Settings** - Uses sensible defaults for all parameters
3. **Errors** - Logged but don't prevent operation

## Best Practices

### 1. Backup Configuration

```bash
cp -r config/ config_backup/
```

### 2. Version Control

Add configuration to git but be careful with sensitive data:

```bash
git add config/prompts.yaml config/settings.yaml
# Don't commit API keys or sensitive settings
```

### 3. Environment-Specific Settings

Use different configurations for different environments:

```bash
# Development
cp config/settings.yaml config/settings.dev.yaml

# Production
cp config/settings.yaml config/settings.prod.yaml
```

### 4. Testing Configuration Changes

Test configuration changes before deployment:

```python
from config.config_loader import ConfigLoader

# Test new configuration
loader = ConfigLoader()
config = loader.load_all_configs()

if config.is_valid:
    print("✅ Configuration is valid")
else:
    print("❌ Configuration has errors")
    for error in config.errors:
        print(f"  - {error}")
```

## Troubleshooting

### Common Issues

1. **YAML Syntax Errors**
   ```
   Error: Invalid YAML in config/prompts.yaml: ...
   ```
   Solution: Check YAML syntax, especially indentation

2. **Missing Placeholders**
   ```
   Error: Missing placeholder {context} in realtime_prompt
   ```
   Solution: Ensure prompts contain required placeholders

3. **Invalid Settings**
   ```
   Error: Missing field 'name' in gemini model 0
   ```
   Solution: Check settings structure matches expected format

### Debug Mode

Enable debug logging to see configuration loading:

```python
import logging
logging.basicConfig(level=logging.DEBUG)

from config.config_loader import load_config
config = load_config()
```

## Migration from Hardcoded Values

If you're upgrading from a version with hardcoded prompts/settings:

1. **Backup existing code**
2. **Run with default configuration** - should work without changes
3. **Gradually customize** - modify configuration files as needed
4. **Test thoroughly** - ensure behavior matches expectations

The system maintains backward compatibility, so existing code continues to work unchanged.

## Speaker Diarization Best Practices

Based on [Deepgram's speaker diarization guide](https://deepgram.com/learn/creating-speaker-labeled-transcripts-with-google-colab), here are recommendations for optimal speaker labeling:

### Audio Quality Requirements

For best diarization results, ensure:

```yaml
audio:
  ffmpeg:
    sample_rate: 16000  # Minimum 16kHz for good diarization
    channels: 1         # Mono audio works best

api:
  deepgram:
    diarization:
      enabled: true
      min_speakers: 2   # Set realistic expectations
      max_speakers: 8   # Don't exceed actual speaker count
```

### Meeting Environment Tips

1. **Audio Quality**
   - Use high-quality microphones
   - Minimize background noise
   - Avoid speaker overlap/cross-talk

2. **Speaker Characteristics**
   - Ensure speakers have distinct voices
   - Maintain consistent speaking volume
   - Minimize echo and reverb

3. **Configuration Tuning**
   ```yaml
   api:
     deepgram:
       diarization:
         enabled: true      # Enable speaker diarization
         min_speakers: 2    # Minimum expected speakers
         max_speakers: 8    # Maximum expected speakers

       # Note: Advanced features like paragraphs, utterances, sentiment, and topics
       # are only available for batch/pre-recorded transcription, not live streaming
   ```

### Troubleshooting Diarization Issues

**Poor speaker separation?**
- Check `min_speakers` and `max_speakers` settings
- Verify audio quality (sample rate, noise levels)
- Ensure speakers have distinct vocal characteristics

**Missing speaker labels?**
- Enable `utterances: true` for better speech boundary detection
- Check that `diarize: true` is set in Deepgram settings
- Verify audio has multiple distinct speakers

**Inconsistent speaker IDs?**
- This is normal - Deepgram assigns Speaker 0, Speaker 1, etc.
- Speaker IDs may not be consistent across different audio files
- Consider post-processing for speaker name mapping if needed

### Live vs Batch Transcription Features

**Live Transcription (daily-catalyst)** supports:
```yaml
api:
  deepgram:
    model: "nova-3"       # High-accuracy model
    diarize: true         # Speaker identification
    smart_format: true    # Automatic formatting
    punctuate: true       # Punctuation
    numerals: true        # Number formatting

    diarization:
      enabled: true
      min_speakers: 2     # Adjust based on your meeting size
      max_speakers: 8     # Don't set too high - affects accuracy
```

**Batch Transcription** additionally supports:
- `paragraphs: true` - Group sentences into logical paragraphs
- `utterances: true` - Detect natural speech boundaries
- `sentiment: true` - Add sentiment analysis per speaker
- `topics: true` - Detect topic changes in conversation

**Note**: The daily-catalyst project focuses on live/real-time transcription, so advanced batch-only features are not available during live streaming but can be configured for future batch processing capabilities.

## Alternative Connection Methods

### Direct WebSocket Connection

Inspired by [AI Scribe examples](https://github.com/deepgram-devs/medical-scribe), you can configure direct WebSocket connection to Deepgram:

```yaml
api:
  deepgram:
    websocket:
      enable_direct_websocket: true
      url_template: "wss://api.deepgram.com/v1/listen?punctuate=true&diarize=true&model={model}&encoding=linear16&sample_rate=16000"
      auth_header: "Authorization: Token {api_key}"
```

**Benefits of Direct WebSocket:**
- Lower latency than SDK
- More control over connection
- Simpler async architecture
- Better for real-time applications

### Live Microphone Capture

Enable live microphone input for real-time meetings:

```yaml
audio:
  source:
    microphone:
      enable_live_mic: true
      device_index: null      # Auto-detect default microphone
      chunk_size: 8000        # Larger chunks for real-time
      format: "paInt16"       # PyAudio format
```

**Use Cases:**
- Live meeting transcription
- Real-time note taking
- Interactive voice analysis

### Async Processing Mode

Enable async architecture for better performance:

```yaml
performance:
  async_processing:
    enable_async_mode: true
    queue_size: 1000
    task_timeout: 30
    concurrent_tasks: 3  # sender, receiver, microphone
```

**Architecture Pattern (from AI Scribe):**
```python
tasks = [
    asyncio.ensure_future(sender(ws, audio_queue)),
    asyncio.ensure_future(receiver(ws)),
    asyncio.ensure_future(microphone(audio_queue)),
]
await asyncio.gather(*tasks)
```

These settings are already integrated into the daily-catalyst configuration system and can be adjusted in `config/settings.yaml` without code changes.

## Real-Time Performance Optimization

Based on [Deepgram's real-time transcription guide](https://deepgram.com/learn/build-a-real-time-transcription-app-with-react-and-deepgram), here are optimization recommendations:

### Chunk Size Optimization

For optimal real-time performance, adjust chunk duration based on your use case:

```yaml
audio:
  streaming:
    # Ultra-responsive (best for real-time UI feedback)
    chunk_duration_ms: 250

    # Balanced (good for most applications)
    chunk_duration_ms: 500

    # High accuracy (better for speaker diarization)
    chunk_duration_ms: 1000
```

**Current daily-catalyst setting**: 250ms for maximum responsiveness

### WebSocket Connection Optimization

```yaml
api:
  deepgram:
    model: "nova-3"        # Recommended for real-time (high accuracy + low latency)
    encoding: "linear16"   # Optimal for WebSocket streaming
    sample_rate: 16000     # Standard for speech recognition
    channels: 1            # Mono audio for better performance

performance:
  timeouts:
    deepgram_connection: 30  # Allow time for WebSocket handshake
```

### Audio Quality vs Performance Trade-offs

| Setting | Responsiveness | Accuracy | Speaker Diarization |
|---------|---------------|----------|-------------------|
| 250ms chunks | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐ |
| 500ms chunks | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 1000ms chunks | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |

### Browser-Based Real-Time Transcription

If you want to implement browser-based real-time transcription (similar to the React article), you can use these APIs:

```javascript
// Access microphone
const stream = await navigator.mediaDevices.getUserMedia({ audio: true });

// Create MediaRecorder with optimal settings
const mediaRecorder = new MediaRecorder(stream);
mediaRecorder.start(250); // 250ms chunks for responsiveness

// WebSocket connection to Deepgram
const socket = new WebSocket('wss://api.deepgram.com/v1/listen?model=nova-3&token=YOUR_API_KEY');

// Send audio chunks
mediaRecorder.ondataavailable = (event) => {
  if (socket.readyState === WebSocket.OPEN) {
    socket.send(event.data);
  }
};
```

**Note**: The daily-catalyst project uses Python with FFmpeg for audio capture, which provides more control over audio processing and is better suited for server-side applications.