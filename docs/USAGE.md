# Daily Catalyst Usage Guide

## Quick Start

### 1. Installation

```bash
# Clone the repository
git clone https://github.com/Dmitry-Chekh/daily-catalyst.git
cd daily-catalyst

# Install dependencies
pip install -r requirements.txt

# Optional: Install in development mode
pip install -e .
```

### 2. Prerequisites for Live Transcription

Before using live transcription, ensure you have:

1. **API Keys** (set in `.env` file):
   ```bash
   DEEPGRAM_API_KEY=your_deepgram_api_key
   GEMINI_API_KEY=your_gemini_api_key
   ```

2. **System Requirements**:
   - FFmpeg installed (`sudo apt install ffmpeg` on Ubuntu)
   - PulseAudio for audio capture
   - Video file in supported format (mp4, avi, mkv, etc.)

3. **Audio Setup**:
   - The script captures audio from `RDPSink.monitor` by default
   - You can change audio source via `AUDIO_SOURCE` environment variable

### 3. Basic Usage

#### Complete Pipeline (Recommended)
```bash
# Run the complete analysis pipeline
python daily_catalyst.py pipeline --audio-file your_meeting.wav
```

This will:
1. Create speaker embeddings (if not exist)
2. Generate complete transcript with speaker identification
3. Analyze meeting for off-topic discussions
4. Generate comprehensive reports

#### Step-by-Step Usage

**Step 1: Create Speaker Embeddings**
```bash
python daily_catalyst.py embeddings --audio-file your_meeting.wav
```

**Step 2: Create Transcript**
```bash
python daily_catalyst.py transcript --audio-file your_meeting.wav
```

**Step 3: Analyze Meeting**
```bash
python daily_catalyst.py analyze
```

### 3. Real-time Processing

#### Live Video Transcription (Recommended)
```bash
# Real-time transcription from video with off-topic detection
python live_transcription.py test_video/meeting_video.mp4

# Or with your own video file
python live_transcription.py path/to/your/meeting.mp4
```

This will:
- Play video with audio output
- Capture audio in real-time from system audio
- Transcribe speech with speaker identification
- Analyze content for off-topic discussions using multiple AI models
- Automatically switch between models when quotas are exceeded
- Provide meeting quality assessment

#### Audio File Processing
```bash
# Real-time processing with speaker identification
python daily_catalyst.py realtime --audio-file your_meeting.wav --speaker-method embeddings
```

## Advanced Usage

### Configuration

Edit `config.yaml` to customize:

```yaml
# Audio settings
audio:
  chunk_duration: 4.0  # Optimal for speaker recognition
  
# Speaker identification
speaker_identification:
  method: "embeddings"  # Most accurate
  similarity_threshold: 0.7
  
# Analysis settings
analysis:
  off_topic:
    threshold: 0.6
```

### Command Line Options

```bash
# Show help
python daily_catalyst.py --help

# Verbose logging
python daily_catalyst.py pipeline --audio-file meeting.wav --verbose

# Custom chunk duration
python daily_catalyst.py transcript --audio-file meeting.wav --chunk-duration 3.0

# Different speaker methods
python daily_catalyst.py realtime --audio-file meeting.wav --speaker-method callhome
```

## Output Files

### Generated Files

1. **Speaker Embeddings**
   - `improved_speaker_embeddings.pkl` - Speaker voice profiles
   - `improved_speaker_embeddings.json` - Human-readable format

2. **Transcripts**
   - `complete_transcript_with_speakers_YYYYMMDD_HHMMSS.json` - Full data
   - `complete_transcript_readable_YYYYMMDD_HHMMSS.txt` - Human-readable
   - `transcript_for_llm_analysis_YYYYMMDD_HHMMSS.txt` - LLM analysis format

3. **Analysis Reports**
   - `daily_meeting_analysis_YYYYMMDD_HHMMSS.json` - Full analysis data
   - `daily_meeting_report_YYYYMMDD_HHMMSS.txt` - Summary report

### Example Output

#### Live Transcription Output
```
🎙️  Live Transcription with Real-Time Off-Topic Analysis
=================================================================
🤖 Powered by Deepgram + Google Gemini Flash 2.5
=================================================================

[0.1s] ✅ Speaker 0: Good morning, my dear team.
[2.3s] ✅ Speaker 0: Let's start the stand up and hope it won't take more than 15 minutes.
[25.4s] 🚨 Speaker 1: then another meeting about improving our meeting efficiency.
         ⚠️  OFF-TOPIC: Meta-discussion about process
[91.2s] 🚨 Speaker 3: Which had a width of 0 because of a missing prop.
         ⚠️  OFF-TOPIC: Excessive technical detail

=================================================================
🎙️  Live transcription with real-time analysis ended
=================================================================
📊 Transcription Stats:
   • Final transcripts: 55
   • Duration: 157.4s
🤖 Analysis Stats:
   • Off-topic segments: 2
   • Off-topic ratio: 3.6%
🎯 Meeting Quality: Excellent focus!
=================================================================
```

#### Batch Analysis Output
```
📊 MEETING ANALYSIS RESULTS
==================================================
🎯 Quality Score: 75/100
⏱️ Duration: 12:34
📈 Efficiency: 68.5%
🔄 Off-topic discussions: 3 instances
🔧 Technical deep-dives: 1 instance

👥 SPEAKER STATISTICS:
SPEAKER_01: 45.2% (5:42) - 21 segments
SPEAKER_02: 28.7% (3:36) - 14 segments
SPEAKER_03: 26.1% (3:16) - 12 segments

🚨 DETECTED ISSUES:
[03:45] Off-topic: Discussion about lunch plans
[08:12] Deep-dive: 5-minute technical architecture debate
[11:20] Off-topic: Weekend project discussion

💡 RECOMMENDATIONS:
• Keep status updates under 2 minutes per person
• Move technical discussions to separate meetings
• Use a timer to track speaking time
```

## Troubleshooting

### Common Issues

**1. Import Errors**
```bash
# Install missing dependencies
pip install speechbrain torch torchaudio
pip install pyannote.audio
```

**2. Audio File Issues**
```bash
# Convert audio to supported format
ffmpeg -i input.mp3 -ar 16000 -ac 1 output.wav
```

**3. Speaker Recognition Issues**
- Ensure audio quality is good (16kHz, mono)
- Use chunk duration 3-5 seconds for best results
- Create embeddings from a representative meeting first

**4. Memory Issues**
```bash
# Reduce chunk duration
python daily_catalyst.py pipeline --audio-file meeting.wav --chunk-duration 2.0
```

### Performance Tips

1. **Audio Quality**
   - Use 16kHz sample rate
   - Mono channel preferred
   - Clear audio without background noise

2. **Speaker Recognition**
   - Create embeddings from a full meeting first
   - Use 4-second chunks for optimal accuracy
   - Ensure speakers talk for at least 10-15 seconds total

3. **Processing Speed**
   - Use GPU if available (CUDA)
   - Reduce chunk duration for faster processing
   - Process shorter segments for testing

## Integration

### Python API

```python
from daily_catalyst import DailyCatalyst

# Initialize
catalyst = DailyCatalyst(config_file="config.yaml")

# Create embeddings
catalyst.create_embeddings("meeting.wav")

# Analyze meeting
results = catalyst.analyze_meeting("meeting.wav")

# Get summary
summary = results.get_summary()
print(f"Quality Score: {summary.quality_score}")
print(f"Off-topic instances: {len(summary.off_topic_discussions)}")
```

### Batch Processing

```python
import glob
from daily_catalyst import DailyCatalyst

catalyst = DailyCatalyst()

# Process multiple meetings
for audio_file in glob.glob("meetings/*.wav"):
    print(f"Processing {audio_file}...")
    results = catalyst.analyze_meeting(audio_file)
    
    # Save results
    results.save_report(f"reports/{audio_file}.txt")
```

## Best Practices

### Meeting Recording

1. **Audio Quality**
   - Use good microphones
   - Minimize background noise
   - Record in quiet environment

2. **Meeting Format**
   - Follow standard standup format
   - Encourage clear speaking
   - Avoid overlapping conversations

### Analysis

1. **Speaker Embeddings**
   - Create from representative meetings
   - Update periodically with new data
   - Use meetings with clear speaker separation

2. **Regular Monitoring**
   - Analyze meetings consistently
   - Track trends over time
   - Use insights for team improvement

### Team Adoption

1. **Start Small**
   - Begin with one team
   - Demonstrate value with examples
   - Gather feedback and iterate

2. **Training**
   - Show team the reports
   - Explain metrics and recommendations
   - Use insights for process improvement
